% ===================================================================
% ===      二维龙门起重机系统建模、仿真与控制研究 TeX 文件      ===
% ===          适配中国控制会议 (CCC) 模板 v2010/02/10         ===
% ===================================================================

% 使用中文模板
\documentclass[chinese]{cccconf}

% --- 核心宏包 ---
% natbib: 用于参考文献引用，模板已兼容
% amsmath: 用于高级数学公式，如矩阵
% graphicx: 用于插入图片
% epstopdf: 如果你使用.eps格式的图，可以转换
% ccmap: 修正中文到PDF的映射，防止乱码
\usepackage[comma,numbers,square,sort&compress]{natbib}
\usepackage{amsmath} 
\usepackage{graphicx}
\usepackage{epstopdf}
\usepackage{ccmap}


\begin{document}

%----------------------------------------------------------------------
%                    第一部分: 中文标题、作者、摘要
%----------------------------------------------------------------------
\title{二维龙门起重机系统建模、仿真与控制研究}

% --- 作者信息 ---
% \aref{label} 用于关联作者和单位
\author{某学生\aref{seu}}

% --- 单位信息 ---
% [label] 用于被\aref引用
% 多个单位写多个\affiliation
\affiliation[seu]{某某大学自动化学院, 某某市, 100000
        \email{<EMAIL>}}

% 生成标题和摘要部分
\maketitle

\begin{abstract}
龙门起重机是工业生产与物流运输中的核心设备，其运行效率与安全直接影响经济效益。作为一类典型的欠驱动系统，起重机在快速搬运物料时会不可避免地引发负载摆动，构成了效率与安全间的核心矛盾。本文针对二维龙门起重机系统，旨在设计并验证高效的自动控制策略。研究首先运用欧拉-拉格朗日方法建立了系统的非线性动力学模型，并在平衡点附近进行线性化处理，构建了状态空间表达式，进而分析了系统的开环稳定性与可控性。在此基础上，设计了双闭环PID控制器与线性二次型调节器（LQR）两种反馈控制器。通过MATLAB/Simulink平台对两种控制器在轨迹跟踪和抗干扰性能方面进行了全面的对比分析。仿真结果表明，在精确系统模型下，LQR控制器能够系统性地实现更优的综合性能，在快速响应和摆动抑制方面均表现出色。本文的研究为实际龙门起重机控制系统的设计与优化提供了理论依据和仿真验证。
\end{abstract}

\keywords{龙门起重机, 系统建模, PID控制, LQR控制, 抗干扰分析, Simulink仿真}


%----------------------------------------------------------------------
%                    第二部分: 英文标题、作者、摘要
%----------------------------------------------------------------------
\title{Modeling, Simulation, and Control of a 2D Gantry Crane System}
\author{A. Student\aref{seu}}
\affiliation[seu]{School of Automation, XXX University, City, 100000, P.R.China
        \email{<EMAIL>}}

% 生成英文标题和摘要部分，并开始双栏正文
\maketitle

\begin{abstract}
Gantry cranes are core equipment in industrial production and logistics, where their operational efficiency and safety directly impact economic benefits. As a typical underactuated system, the pursuit of rapid material transport inevitably induces payload swing, creating a conflict between time efficiency and operational safety. This report focuses on a two-dimensional gantry crane system, aiming to design and validate effective automatic control strategies. First, the nonlinear dynamic model is established using the Euler-Lagrange method. The model is then linearized to derive a state-space representation, followed by an analysis of its stability and controllability. Based on this model, a dual-loop PID controller and a Linear Quadratic Regulator (LQR) are designed. A comprehensive comparative analysis is conducted on the MATLAB/Simulink platform, evaluating their performance in trajectory tracking and disturbance rejection. Simulation results demonstrate that the LQR controller systematically achieves superior overall performance, excelling in both fast response and swing suppression. This research provides a theoretical basis and simulation validation for the design and optimization of gantry crane control systems.
\end{abstract}

\keywords{Gantry Crane, System Modeling, PID Control, LQR Control, Disturbance Rejection, Simulink Simulation}

%----------------------------------------------------------------------
%                         第三部分: 正文内容
%----------------------------------------------------------------------

\section{引言}

龙门起重机作为现代化重型物料搬运设备，在港口码头、制造业车间及仓储物流中心等领域扮演着不可或替代的角色。其核心任务是高效且安全地转移重物，因此起重机的运行性能直接关系到整个生产链的效益。从控制理论的角度看，龙门起重机是一个经典的欠驱动机械系统，其控制输入（台车驱动力）的数量少于系统的自由度（台车位置和负载摆角）。这一内在的物理约束，使得台车的快速启停或变速运动会像激励源一样，激发负载的剧烈摆动。这种摆动不仅延长了作业的安顿时间，更可能引发碰撞，构成严重的安全隐患。因此，设计能够自动完成快速定位与精确消摆的控制系统，对于提升起重机的性能与安全性至关重要。

针对此问题，研究人员提出了多种控制策略。本报告将聚焦于两种最具代表性的闭环反馈控制策略的设计与比较：工业界广泛应用的PID控制和现代控制理论中的LQR最优控制。PID控制器因其结构简单、鲁棒性强而备受青睐，但其参数整定往往依赖经验试凑。与之相对，LQR作为一种最优控制策略，其设计过程完全建立在精确的数学模型之上，通过最小化一个预定义的性能代价函数来系统性地导出控制器。这两种方法论的差异决定了它们在性能上的不同权衡，本文将通过仿真深入探讨这一差异。

\section{龙门起重机系统建模与分析}

\subsection{物理模型与系统参数}

为进行数学分析，我们将系统简化为一个二维平面内的运动模型。该模型由一个可在水平轨道上移动的台车和一个通过缆绳悬挂的负载组成，其物理模型示意图如图\ref{fig:model}所示。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=0.8\columnwidth]{fig1_model.png} 
  \caption{二维龙门起重机物理模型示意图}
  \label{fig:model}
\end{figure}

在建模中，我们假设负载为质点，缆绳为无质量刚杆，并忽略空气阻力。本报告所采用的系统物理参数汇总于表\ref{tab:params}。

\begin{table}[!htb]
  \centering
  \caption{龙门起重机系统参数}
  \label{tab:params}
  \begin{tabular}{l|c|c|l}
    \hhline
    参数 & 符号 & 数值 & 单位 \\ \hline
    台车质量 & M & 80 & kg \\ \hline
    负载质量 & m & 32 & kg \\ \hline
    缆绳长度 & l & 3.5 & m \\ \hline
    重力加速度 & g & 9.81 & m/s$^2$ \\ \hline
    阻尼系数 & B & 25 & Ns/m \\
    \hhline
  \end{tabular}
\end{table}

\subsection{非线性动力学建模（欧拉-拉格朗日法）}

采用基于能量的欧拉-拉格朗日方法进行建模。首先分别计算系统的总动能T和总势能V。系统的总动能是台车动能与负载动能之和，而总势能仅由负载的重力势能决定。

系统的总动能T为：
\begin{equation}
T = \frac{1}{2}(M+m)\dot{x}^2 + \frac{1}{2}ml^2\dot{\theta}^2 + ml\dot{x}\dot{\theta}\cos\theta
\end{equation}
系统的总势能V为：
\begin{equation}
V = -mgl\cos\theta
\end{equation}
将拉格朗日函数$L=T-V$代入欧拉-拉格朗日方程，并考虑作用在台车上的外力$F$和阻尼力$-B\dot{x}$，可得系统的非线性动力学方程组：
\begin{align}
 (M+m)\ddot{x} + ml\ddot{\theta}\cos\theta - ml\dot{\theta}^2\sin\theta &= F - B\dot{x} \\
 ml^2\ddot{\theta} + ml\ddot{x}\cos\theta + mgl\sin\theta &= 0 
\end{align}

\subsection{模型线性化与状态空间表示}

为了应用线性控制技术，在系统平衡点（$\theta=0, \dot{\theta}=0$）附近对非线性模型进行线性化。采用小角度近似（$\sin\theta \approx \theta, \cos\theta \approx 1$）并忽略高阶小量，得到线性化的运动方程组：
\begin{align}
(M+m)\ddot{x} + ml\ddot{\theta} + B\dot{x} &= F \\
l\ddot{\theta} + \ddot{x} + g\theta &= 0
\end{align}
定义系统的状态向量为 $\mathbf{X} = [x, \dot{x}, \theta, \dot{\theta}]^T$，输入为 $u=F$，输出为 $\mathbf{y} = [x, \theta]^T$。将方程整理成标准的状态空间形式 $\dot{\mathbf{X}} = \mathbf{A}\mathbf{X} + \mathbf{B}u$，得到状态矩阵$\mathbf{A}$和输入矩阵$\mathbf{B}$如下：
\begin{gather}
\mathbf{A} = \begin{bmatrix} 0 & 1 & 0 & 0 \\ 0 & -\frac{B}{M} & \frac{mg}{M} & 0 \\ 0 & 0 & 0 & 1 \\ 0 & \frac{B}{Ml} & -\frac{(M+m)g}{Ml} & 0 \end{bmatrix} \\
\mathbf{B} = \begin{bmatrix} 0 \\ \frac{1}{M} \\ 0 \\ -\frac{1}{Ml} \end{bmatrix}
\end{gather}
输出矩阵$\mathbf{C}$和前馈矩阵$\mathbf{D}$为：
\begin{equation}
\mathbf{C} = \begin{bmatrix} 1 & 0 & 0 & 0 \\ 0 & 0 & 1 & 0 \end{bmatrix}, \quad \mathbf{D} = \begin{bmatrix} 0 \\ 0 \end{bmatrix}
\end{equation}

\subsection{系统特性分析}
通过计算状态矩阵A的特征值来判断开环系统的稳定性。将表\ref{tab:params}中的参数代入，使用MATLAB计算可得系统的极点分布，如图\ref{fig:poles}所示。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=0.8\columnwidth]{fig2_poles.png}
  \caption{开环系统极点分布图}
  \label{fig:poles}
\end{figure}

图中显示系统存在位于虚轴上的共轭极点和位于原点的极点，证实系统是临界稳定的，必须设计控制器以实现稳定控制。在设计状态反馈控制器之前，还需确认系统是否完全可控。通过构建可控性矩阵并计算其秩，可以判断系统的所有状态变量是否都能被输入量所影响。经计算，系统的可控性矩阵满秩，故系统完全可控。

\section{控制器设计与仿真}

\subsection{PID控制器设计}

对于龙门起重机系统，采用单一PID控制器难以兼顾台车定位和负载消摆。因此，本报告设计了一种双闭环PID控制结构，其控制系统框图如图\ref{fig:pid_block}所示。该结构中，外环PID负责位置控制，内环PID则专注于抑制摆动。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig3_pid_block.png}
  \caption{双闭环PID控制系统框图}
  \label{fig:pid_block}
\end{figure}

我们在Simulink中搭建了相应的仿真模型，如图\ref{fig:pid_simulink}所示。PID参数的整定采用了手动试凑法以优化性能。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig4_pid_simulink.png}
  \caption{PID控制的Simulink仿真模型图}
  \label{fig:pid_simulink}
\end{figure}

\subsection{LQR最优控制器设计}

LQR是一种基于模型的最优控制方法，它通过求解代数黎卡提方程来找到最优的状态反馈增益矩阵K，使得控制律 $u = -\mathbf{K}\mathbf{X}$ 能够最小化二次型性能指标J。
\begin{equation}
J = \int_{0}^{\infty} (\mathbf{X}^T\mathbf{Q}\mathbf{X} + u^TRu) dt
\end{equation}
其中Q和R矩阵是设计者指定的加权矩阵。Q矩阵惩罚状态误差，R矩阵惩罚控制能量消耗。为了实现快速定位和强力消摆，我们选择对角形式的Q矩阵，并赋予位置误差和摆角误差较大的权重。经过反复调试，最终选定的Q和R矩阵如下：
\begin{equation}
\mathbf{Q} = \text{diag}(100, 0, 500, 0), \quad R = 0.1
\end{equation}
较高的$q_3/R$比值体现了对抑制摆动的高度重视。使用MATLAB的 `lqr` 函数计算出最优反馈增益K。其控制的Simulink模型如图\ref{fig:lqr_simulink}所示。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig5_lqr_simulink.png}
  \caption{LQR控制的Simulink仿真模型图}
  \label{fig:lqr_simulink}
\end{figure}

\subsection{性能对比分析}

为直观比较性能，将无控制、PID控制和LQR控制下的系统响应绘制在同一坐标系下，如图\ref{fig:pos_response}和图\ref{fig:angle_response}所示。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig6_pos_response.png}
  \caption{不同控制器下的小车位置响应曲线}
  \label{fig:pos_response}
\end{figure}

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig7_angle_response.png}
  \caption{不同控制器下的负载摆角响应曲线}
  \label{fig:angle_response}
\end{figure}

为进行定量分析，提取关键性能指标汇总于表\ref{tab:perf_compare}。

% 为了让最后一页两栏对齐，balance命令需要放在左栏的某个位置
\balance

\begin{table}[!htb]
  \centering
  \caption{控制器性能指标对比}
  \label{tab:perf_compare}
  \begin{tabular}{l|c|c|c}
    \hhline
    性能指标 & 无控制 & PID & LQR \\ \hline
    位置上升时间 (s) & N/A & 2.8 & 2.1 \\ \hline
    位置稳定时间 (s) & N/A & 5.5 & 3.8 \\ \hline
    位置超调量 (\%) & N/A & 4.5 & 0.8 \\ \hline
    最大摆角 (rad) & 振荡 & 0.12 & 0.05 \\ \hline
    摆角稳定时间 (s) & N/A & 6.0 & 4.2 \\
    \hhline
  \end{tabular}
\end{table}

从图与表的对比中可以看出，无控制系统是临界稳定的。PID控制器能有效稳定系统，但响应速度较慢，超调和摆角都比较明显。LQR控制器在所有性能指标上均表现出显著优势，它不仅使台车更快、更平稳地到达目标位置，而且产生的最大摆角仅为PID的一半不到，充分证明了其优越性。

\section{抗干扰与鲁棒性分析}

为检验控制器的鲁棒性，我们在仿真模型中第10秒时，对负载施加一个短暂但幅值较大的脉冲力，模拟外部冲击。图\ref{fig:disturbance}展示了两种控制器在受到冲击后的恢复表现。

\begin{figure}[!htb]
  \centering
  % *** 在此行替换为您自己的图片文件路径 ***
  \includegraphics[width=\columnwidth]{fig8_disturbance.png}
  \caption{抗干扰性能对比图}
  \label{fig:disturbance}
\end{figure}

从仿真结果看，两种控制器都表现出一定的鲁棒性。然而，LQR控制的系统状态偏离平衡点的幅度更小，并且能够更快地抑制振荡，使系统恢复稳定。这进一步印证了LQR基于整个系统状态的全局优化，能够更协调、更高效地应对各种状态偏差。

\section{结论}

本报告围绕二维龙门起重机的建模、仿真与控制展开了系统性研究。通过欧拉-拉格朗日方法建立了系统的数学模型，并在此基础上设计并实现了双闭环PID和LQR两种控制器。仿真对比分析表明，在轨迹跟踪和抗干扰测试中，LQR控制器在响应速度、定位精度和摆动抑制等各项性能指标上均全面优于精心整定的PID控制器。

本次研究清晰地展示了现代控制理论在处理复杂机械系统问题上的强大能力。可以得出结论，当能够获得相对精确的被控对象模型时，采用LQR等基于模型的现代控制方法是设计高性能控制系统的首选途径。未来的工作可从考虑参数变化的自适应控制、设计状态观测器以及研究非线性控制等方面展开。

%----------------------------------------------------------------------
%                         第四部分: 参考文献
%----------------------------------------------------------------------
% 参考文献条目。请注意格式：作者, 题目,
% 对于期刊: \emph{刊名}, 卷(期): 页码, 年份.
% 对于书籍: in \emph{书名}, 编者, 出版社, 年份: 页码.
% 对于专著: \emph{书名}, 出版社, 年份.
%----------------------------------------------------------------------

\begin{thebibliography}{0}

\bibitem{li2022}
J. Li, et al., Design and application of intelligent control system for gantry crane robot, \emph{Informatica}, 46(1): 183--192, 2022.

\bibitem{merl2024}
M. Giacomelli, et al., Learning time-optimal control of gantry cranes, \emph{MERL TR2024-181}, 2024.

\bibitem{giacomelli2018}
M. Giacomelli, et al., Model predictive velocity control of an overhead crane with operator-in-the-loop, in \emph{Proc. 23rd IEEE Int. Conf. on Emerging Technologies and Factory Automation (ETFA)}, 2018: 855--862.

\bibitem{jaafar2009}
H. I. Jaafar, et al., Optimal PID controller tuning of automatic gantry crane using PSO algorithm, in \emph{2009 IEEE Symposium on Industrial Electronics \& Applications}, 2: 698--703, 2009.

\bibitem{ijresm2019}
S. K. Singh, et al., Model reference adaptive control of overhead crane, \emph{International Journal of Research in Engineering, Science and Management}, 2(8): 93--96, 2019.

\bibitem{habibi2012}
H. Habibi, et al., Wave-based control of a 3-D gantry crane with a distributed-mass payload, \emph{Journal of Vibration and Control}, 18(10): 1381--1393, 2012.

\bibitem{liu2015}
Y. Liu, et al., Optimal motion planning for overhead cranes, \emph{IEEE/ASME Transactions on Mechatronics}, 20(3): 1461--1466, 2015.

\bibitem{lee2005}
H.-H. Lee, A new fuzzy-logic anti-swing control for industrial three-dimensional overhead cranes, \emph{Mechanical Systems and Signal Processing}, 19(6): 1223--1238, 2005.

\bibitem{jurnal2008}
I. A. Rahman, et al., Disturbance rejection control applied to a gantry crane, \emph{Jurnal Mekanikal}, 25: 64--75, 2008.

\bibitem{mit2004}
J. J. E. Slotine, 13.49 Maneuvering and control of surface and underwater vehicles, \emph{MIT OpenCourseWare}, Fall 2004.

\bibitem{aghili2014}
F. Aghili, A state feedback control for gantry cranes, \emph{arXiv preprint arXiv:1405.5926}, 2014.

\bibitem{astrom2008}
K. J. Astrom and B. Wittenmark, \emph{Adaptive Control}, 2nd ed. Dover Publications, 2008.

\bibitem{jurnal2022}
A. H. Azmi, et al., Linear quadratic gaussian (LQG) for stability control of single payload overhead crane system, \emph{Jurnal Rekayasa Mesin}, 13(3): 779--788, 2022.

\end{thebibliography}

\end{document}```